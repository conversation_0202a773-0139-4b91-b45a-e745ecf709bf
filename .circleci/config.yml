version: 2.1
orbs:
  node: circleci/node@6.1.0

defaults: &defaults
  working_directory: ~/build
  machine:
    image: ubuntu-2204:2024.05.1
  resource_class: large
  environment:
    RAILS_LOG_TO_STDOUT: false
    COVERAGE: true
    LOG_LEVEL: warn
  parallelism: 4

jobs:
  build:
    <<: *defaults
    steps:
      - checkout
      - node/install:
          node-version: '23.7'
      - node/install-pnpm
      - node/install-packages:
          pkg-manager: pnpm
          override-ci-command: pnpm i
      - run: node --version
      - run: pnpm --version
      - run:
          name: Add PostgreSQL repository and update
          command: |
            sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
            wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
            sudo apt-get update -y

      - run:
          name: Install System Dependencies
          command: |
            sudo apt-get update
            DEBIAN_FRONTEND=noninteractive sudo apt-get install -y \
              libpq-dev \
              redis-server \
              postgresql-common \
              postgresql-16 \
              postgresql-16-pgvector \
              build-essential \
              git \
              curl \
              libssl-dev \
              zlib1g-dev \
              libreadline-dev \
              libyaml-dev \
              openjdk-11-jdk \
              jq \
              software-properties-common \
              ca-certificates \
              imagemagick \
              libxml2-dev \
              libxslt1-dev \
              file \
              g++ \
              gcc \
              autoconf \
              gnupg2 \
              patch \
              ruby-dev \
              liblzma-dev \
              libgmp-dev \
              libncurses5-dev \
              libffi-dev \
              libgdbm6 \
              libgdbm-dev \
              libvips

      - run:
          name: Install RVM and Ruby 3.4.4
          command: |
            sudo apt-get install -y gpg
            gpg --keyserver hkp://keyserver.ubuntu.com --recv-keys 409B6B1796C275462A1703113804BB82D39DC0E3 7D2BAF1CF37B13E2069D6956105BD0E739499BDB
            \curl -sSL https://get.rvm.io | bash -s stable
            echo 'source ~/.rvm/scripts/rvm' >> $BASH_ENV
            source ~/.rvm/scripts/rvm
            rvm install "3.4.4"
            rvm use 3.4.4 --default
            gem install bundler -v 2.5.16

      - run:
          name: Install Application Dependencies
          command: |
            source ~/.rvm/scripts/rvm
            bundle install
            # pnpm install

      - run:
          name: Download cc-test-reporter
          command: |
            mkdir -p ~/tmp
            curl -L https://codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ~/tmp/cc-test-reporter
            chmod +x ~/tmp/cc-test-reporter

      # Swagger verification
      - run:
          name: Verify swagger API specification
          command: |
            bundle exec rake swagger:build
            if [[ `git status swagger/swagger.json --porcelain` ]]
            then
              echo "ERROR: The swagger.json file is not in sync with the yaml specification. Run 'rake swagger:build' and commit 'swagger/swagger.json'."
              exit 1
            fi
            curl -L https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/6.3.0/openapi-generator-cli-6.3.0.jar > ~/tmp/openapi-generator-cli-6.3.0.jar
            java -jar ~/tmp/openapi-generator-cli-6.3.0.jar validate -i swagger/swagger.json

      # we remove the FRONTED_URL from the .env before running the tests
      - run:
          name: Database Setup and Configure Environment Variables
          command: |
            pg_pass=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 15 ; echo '')
            sed -i "s/REPLACE_WITH_PASSWORD/${pg_pass}/g" ${PWD}/.circleci/setup_chatwoot.sql
            chmod 644 ${PWD}/.circleci/setup_chatwoot.sql
            mv ${PWD}/.circleci/setup_chatwoot.sql /tmp/
            sudo -i -u postgres psql -f /tmp/setup_chatwoot.sql
            cp .env.example .env
            sed -i '/^FRONTEND_URL/d' .env
            sed -i -e '/REDIS_URL/ s/=.*/=redis:\/\/localhost:6379/' .env
            sed -i -e '/POSTGRES_HOST/ s/=.*/=localhost/' .env
            sed -i -e '/POSTGRES_USERNAME/ s/=.*/=chatwoot/' .env
            sed -i -e "/POSTGRES_PASSWORD/ s/=.*/=$pg_pass/" .env
            echo -en "\nINSTALLATION_ENV=circleci" >> ".env"

      # Database setup
      - run:
          name: Run DB migrations
          command: bundle exec rails db:chatwoot_prepare

      # Bundle audit
      - run:
          name: Bundle audit
          command: bundle exec bundle audit update && bundle exec bundle audit check -v

      # Rubocop linting
      - run:
          name: Rubocop
          command: bundle exec rubocop

      # ESLint linting
      - run:
          name: eslint
          command: pnpm run eslint

      - run:
          name: Run frontend tests
          command: |
            mkdir -p ~/build/coverage/frontend
            ~/tmp/cc-test-reporter before-build
            pnpm run test:coverage

      - run:
          name: Code Climate Test Coverage (Frontend)
          command: |
            ~/tmp/cc-test-reporter format-coverage -t lcov -o "~/build/coverage/frontend/codeclimate.frontend_$CIRCLE_NODE_INDEX.json"

      # Run backend tests
      - run:
          name: Run backend tests
          command: |
            mkdir -p ~/tmp/test-results/rspec
            mkdir -p ~/tmp/test-artifacts
            mkdir -p ~/build/coverage/backend
            ~/tmp/cc-test-reporter before-build
            TESTFILES=$(circleci tests glob "spec/**/*_spec.rb" | circleci tests split --split-by=timings)
            bundle exec rspec --format progress \
                              --format RspecJunitFormatter \
                              --out ~/tmp/test-results/rspec.xml \
                              -- ${TESTFILES}
          no_output_timeout: 30m

      - run:
          name: Code Climate Test Coverage (Backend)
          command: |
            ~/tmp/cc-test-reporter format-coverage -t simplecov -o "~/build/coverage/backend/codeclimate.$CIRCLE_NODE_INDEX.json"

      - run:
          name: List coverage directory contents
          command: |
            ls -R ~/build/coverage

      - persist_to_workspace:
          root: ~/build
          paths:
            - coverage
