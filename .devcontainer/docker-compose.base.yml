# Docker Compose file for building the base image in GitHub Actions
# Usage: docker-compose -f .devcontainer/docker-compose.base.yml build base

version: '3'

services:
  base:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile.base
      args:
        VARIANT: 'ubuntu-22.04'
        NODE_VERSION: '23.7.0'
        RUBY_VERSION: '3.4.4'
        # On Linux, you may need to update USER_UID and USER_GID below if not your local UID is not 1000.
        USER_UID: '1000'
        USER_GID: '1000'
    image: ghcr.io/chatwoot/chatwoot_codespace:latest
