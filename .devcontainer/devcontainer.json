{
  "name": "Chatwoot Development Codespace",
  "service": "app",
  "dockerComposeFile": "docker-compose.yml",

  "settings": {
    "terminal.integrated.shell.linux": "/bin/zsh",
    "extensions.showRecommendationsOnlyOnDemand": true,
    "editor.formatOnSave": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "search.exclude": {
      "**/node_modules": true,
      "**/tmp": true,
      "**/log": true,
      "**/coverage": true,
      "**/public/packs": true
    }
  },


  // Add the IDs of extensions you want installed when the container is created.
  "extensions": [
    "Shopify.ruby-lsp",
    "misogi.ruby-rubocop",
    "davidpallinder.rails-test-runner",
    "github.copilot",
    "mrmlnc.vscode-duplicate"
  ],


  // 5432 postgres
  // 6379 redis
  // 1025,8025 mailhog
  "forwardPorts": [8025, 3000, 3036],

  "postCreateCommand": ".devcontainer/scripts/setup.sh && POSTGRES_STATEMENT_TIMEOUT=600s bundle exec rake db:chatwoot_prepare && pnpm install",
  "portsAttributes": {
    "3000": {
      "label": "Rails Server"
    },
    "3036": {
      "label": "Vite Dev Server"
    },
    "8025": {
      "label": "Mailhog UI"
    }
  }
}
